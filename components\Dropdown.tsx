import React, { useState, useEffect, useMemo } from "react";
import {
  Modal,
  View,
  FlatList,
  Pressable,
  Text,
  TouchableWithoutFeedback,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
  Dimensions,
  TextInput,
} from "react-native";
import globalStyles from "@/lib/globalStyles";
import { screenWidth } from "@/lib/device";
import { useTranslation } from "react-i18next";

export interface DropdownItem {
  id: string;
  label: string;
  value: any;
  icon?: string;
}

interface DropdownProps {
  // Core functionality
  items: DropdownItem[];
  onSelectItem?: (item: DropdownItem) => void;

  // Controlled approach
  visible?: boolean;
  onClose?: () => void;

  // Uncontrolled approach
  onToggle?: (visible: boolean) => void;

  // Positioning
  triggerRef: React.RefObject<any>;
  alignment?: "left" | "right" | "center";

  // Search functionality
  searchable?: boolean;
  searchPlaceholder?: string;

  // Basic styling
  containerStyle?: StyleProp<ViewStyle>;
  itemTextStyle?: StyleProp<TextStyle>;

  // Custom rendering
  renderItem?: (item: DropdownItem) => React.ReactNode;
}

interface DropdownPosition {
  top: number;
  left?: number;
  right?: number;
}

const Dropdown: React.FC<DropdownProps> = ({
  items,
  onSelectItem,
  visible: controlledVisible,
  onClose,
  onToggle,
  triggerRef,
  alignment = "left",
  searchable = false,
  searchPlaceholder,
  containerStyle,
  itemTextStyle,
  renderItem,
}) => {
  const { t } = useTranslation();
  const [internalVisible, setInternalVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [dropdownPosition, setDropdownPosition] = useState<DropdownPosition>({
    top: 0,
    left: 0,
  });

  // Filter items based on search query
  const filteredItems = useMemo(() => {
    if (!searchable || !searchQuery.trim()) {
      return items;
    }

    const query = searchQuery.toLowerCase().trim();
    return items.filter(
      (item) =>
        item.label.toLowerCase().includes(query) ||
        item.value?.toString().toLowerCase().includes(query)
    );
  }, [items, searchQuery, searchable]);

  // Determine if controlled or uncontrolled
  const isControlled = controlledVisible !== undefined;
  const visible = isControlled ? controlledVisible : internalVisible;

  const handleClose = () => {
    setSearchQuery(""); // Clear search when closing
    if (isControlled) {
      onClose?.();
    } else {
      setInternalVisible(false);
      onToggle?.(false);
    }
  };

  const handleSelectItem = (item: DropdownItem) => {
    onSelectItem?.(item);
    handleClose();
  };

  const measureDropdownPosition = () => {
    if (!triggerRef.current) return;

    triggerRef.current.measure(
      (
        _x: number,
        _y: number,
        width: number,
        height: number,
        pageX: number,
        pageY: number
      ) => {
        const { width: screenWidth, height: screenHeight } =
          Dimensions.get("window");
        const dropdownWidth = 200; // Estimated dropdown width, will be dynamic later
        const dropdownHeight = Math.min(items.length * 44 + 16, 300); // Estimated height
        const margin = 10; // Screen edge margin

        let position: DropdownPosition = {
          top: pageY + height + 5,
        };

        // Check if dropdown would go below screen, if so position above trigger
        if (position.top + dropdownHeight > screenHeight - margin) {
          position.top = pageY - dropdownHeight - 5;

          // If still doesn't fit above, position at screen edge
          if (position.top < margin) {
            position.top = margin;
          }
        }

        switch (alignment) {
          case "right":
            position.right = screenWidth - (pageX + width);

            // Ensure dropdown doesn't go off left edge
            if (position.right + dropdownWidth > screenWidth - margin) {
              position.right = margin;
              delete position.left;
            }
            break;

          case "center":
            const centerX = pageX + width / 2;
            const leftPosition = centerX - dropdownWidth / 2;

            // Ensure dropdown stays within screen bounds
            if (leftPosition < margin) {
              position.left = margin;
            } else if (leftPosition + dropdownWidth > screenWidth - margin) {
              position.left = screenWidth - dropdownWidth - margin;
            } else {
              position.left = leftPosition;
            }
            delete position.right;
            break;

          case "left":
          default:
            position.left = pageX;

            // Ensure dropdown doesn't go off right edge
            if (pageX + dropdownWidth > screenWidth - margin) {
              position.left = screenWidth - dropdownWidth - margin;
            }

            // Ensure dropdown doesn't go off left edge
            if (position.left < margin) {
              position.left = margin;
            }
            delete position.right;
            break;
        }

        setDropdownPosition(position);
      }
    );
  };

  useEffect(() => {
    if (visible) {
      measureDropdownPosition();
    }
  }, [visible, alignment]);

  const defaultRenderItem = ({ item }: { item: DropdownItem }) => (
    <Pressable
      style={styles.dropdownItem}
      onPress={() => handleSelectItem(item)}
      android_ripple={{
        color: globalStyles.colors.light.primary,
        borderless: false,
      }}
    >
      <Text style={[styles.dropdownItemText, itemTextStyle]}>{item.label}</Text>
    </Pressable>
  );

  const renderDropdownItem = ({ item }: { item: DropdownItem }) => {
    if (renderItem) {
      return (
        <Pressable onPress={() => handleSelectItem(item)}>
          {renderItem(item)}
        </Pressable>
      );
    }
    return defaultRenderItem({ item });
  };

  if (!visible) return null;

  return (
    <Modal
      transparent={true}
      visible={visible}
      animationType="fade"
      onRequestClose={handleClose}
    >
      <TouchableWithoutFeedback onPress={handleClose}>
        <View style={styles.dropdownOverlay}>
          <View
            style={[styles.dropdownContainer, dropdownPosition, containerStyle]}
          >
            {searchable && (
              <View style={styles.searchInputContainer}>
                <TextInput
                  style={styles.searchInput}
                  placeholder={searchPlaceholder || t("common.search")}
                  placeholderTextColor={globalStyles.colors.tertiary2}
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoFocus={false}
                />
              </View>
            )}
            <FlatList
              data={filteredItems}
              renderItem={renderDropdownItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              style={{ flexGrow: 0 }}
              contentContainerStyle={{ flexGrow: 0 }}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  dropdownOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
  dropdownContainer: {
    position: "absolute",
    backgroundColor: globalStyles.colors.white,
    borderRadius: globalStyles.rounded.xs,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: 150,
    maxWidth: Math.min(screenWidth * 0.8, 300),
    // Add subtle border for better definition
    borderWidth: 1,
    borderColor: globalStyles.rgba({ opacity: 0.1 }).primary1,
  },
  dropdownItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: globalStyles.gap["2xs"],
    gap: globalStyles.gap["2xs"],
    minHeight: 44, // Consistent touch target
  },
  dropdownItemText: {
    fontSize: globalStyles.size.lg,
    color: globalStyles.colors.dark.secondary,
    flex: 1, // Allow text to take available space
  },
  searchInputContainer: {
    paddingHorizontal: globalStyles.gap.xs,
    paddingTop: globalStyles.gap["2xs"],
    paddingBottom: globalStyles.gap["2xs"] / 2,
    borderBottomWidth: 1,
    borderBottomColor: globalStyles.rgba({ opacity: 0.1 }).primary1,
  },
  searchInput: {
    height: 35,
    fontSize: globalStyles.size.sm,
    color: globalStyles.colors.dark.secondary,
    paddingHorizontal: globalStyles.gap["2xs"],
    backgroundColor: globalStyles.colors.light.primary,
    borderRadius: globalStyles.rounded.xs,
  },
});

export default Dropdown;
